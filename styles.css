/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* <PERSON><PERSON><PERSON>-Inspired Color Scheme */
:root {
    --primary-white: #ffffff;
    --secondary-white: #fafafa;
    --tertiary-white: #f5f5f5;
    --primary-black: #000000;
    --secondary-black: #1a1a1a;
    --tertiary-black: #333333;
    --accent-gold: #d4af37;
    --accent-silver: #c0c0c0;
    --text-primary: #000000;
    --text-secondary: #333333;
    --text-tertiary: #666666;
    --border-light: #e5e5e5;
    --border-medium: #cccccc;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.2);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--primary-white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-weight: 300;
    letter-spacing: 0.01em;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    font-size: 16px;
}

/* Optimized Performance & Hardware Acceleration */
* {
    will-change: auto;
}

/* Performance optimization for animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Hardware-accelerated smooth transitions */
a, button, .btn, .cta-button, .pricing-card, .feature-item,
.testimonial-card, .coach-card, .achievement, .certification-item {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimized hover states with hardware acceleration */
.interactive-element {
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translate3d(0, 0, 0);
    will-change: transform;
}

.interactive-element:hover {
    transform: translate3d(0, -2px, 0);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Optimized section transitions */
section {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Performance-optimized scroll animations */
.animate-on-scroll {
    will-change: transform, opacity;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
}

/* Smooth scrolling optimization */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    font-size: 16px;
    overflow-x: hidden;
}

/* Optimized body for ultra-smooth scrolling */
body {
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
}

/* Container optimizations for smooth scrolling */
.container, .section-container {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    contain: layout style paint;
}

/* Optimized card containers */
.card, .pricing-card, .testimonial-card, .feature-card, .benefit-card {
    contain: layout style paint;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
}

/* Performance-critical elements */
.navbar, .hero-section, .footer {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    contain: layout style paint;
}

/* Luxury Typography System - Chanel Inspired */
p, span, div, li, td, th, label {
    color: var(--text-primary);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 300;
    line-height: 1.8;
    letter-spacing: 0.02em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Cinzel', 'Georgia', serif;
    color: var(--text-primary);
    font-weight: 400;
    line-height: 1.3;
    letter-spacing: 0.05em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    margin-bottom: 2rem;
    text-transform: uppercase;
}

/* Luxury heading hierarchy - Chanel Style */
h1 {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 300;
    letter-spacing: 0.1em;
    color: var(--text-primary);
    margin-bottom: 3rem;
}

h2 {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 300;
    letter-spacing: 0.08em;
    color: var(--text-primary);
    margin-bottom: 2.5rem;
}

h3 {
    font-family: 'Inter', sans-serif;
    font-size: clamp(1.2rem, 2.5vw, 1.5rem);
    font-weight: 400;
    letter-spacing: 0.06em;
    text-transform: uppercase;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    letter-spacing: 0.04em;
    text-transform: uppercase;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Enhanced text contrast and readability with luxury theme */
.container, .container *,
.about-content *, .solution-content *,
.program-details *, .pricing-card *,
.about-coach *, .coach-info *,
.certifications *, .coach-story,
.coach-achievements *, .achievement,
.feature-item span, .value-proposition p,
.guarantee-content h4, .guarantee-content p {
    color: var(--text-primary);
}

.hero-description, .section-description {
    font-family: 'Inter', sans-serif;
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--text-secondary);
    font-weight: 300;
    letter-spacing: 0.02em;
}

/* Enhanced Image Quality & Presentation */
img {
    max-width: 100%;
    height: auto;
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: translateZ(0);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* High-quality image containers */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.image-container:hover img {
    transform: scale(1.05);
}

/* Smooth loading transitions */
.image-loading {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.image-loaded {
    opacity: 1;
    transform: scale(1);
}

/* Enhanced coach photo styling */
.coach-photo {
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.coach-photo:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Luxury Card Styling System - Chanel Inspired */
.card, .pricing-card, .testimonial-card, .feature-card, .benefit-card {
    background: var(--primary-white);
    border: 1px solid var(--border-light);
    border-radius: 0;
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: all 0.3s ease;
    transform: translateZ(0);
    position: relative;
}

/* Luxury Card Text */
.card h3, .pricing-card h3, .testimonial-card h3,
.feature-card h3, .benefit-card h3 {
    color: var(--text-primary);
    font-family: 'Cinzel', serif;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.card p, .pricing-card p, .testimonial-card p,
.feature-card p, .benefit-card p {
    color: var(--text-secondary);
    font-family: 'Inter', sans-serif;
    font-weight: 300;
}

.card span, .pricing-card span, .testimonial-card span,
.feature-card span, .benefit-card span {
    color: var(--text-primary);
}

.card:hover, .pricing-card:hover, .testimonial-card:hover,
.feature-card:hover, .benefit-card:hover {
    transform: translateY(-5px) translateZ(0);
    background: var(--secondary-white);
    box-shadow: 0 4px 16px var(--shadow-medium);
    border-color: var(--border-medium);
}

/* Premium Cinematic Section Styling */
.section {
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 107, 53, 0.03) 0%, transparent 50%),
        linear-gradient(180deg, rgba(255, 255, 255, 0.01) 0%, transparent 100%);
    pointer-events: none;
    z-index: -1;
}

/* Cinematic Text Styling */
.section-title {
    font-family: 'Cinzel', 'Georgia', serif;
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2rem;
    color: var(--text-primary);
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.02em;
}

.section-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    line-height: 1.8;
    color: var(--text-secondary) !important;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    letter-spacing: 0.01em;
}

/* Optimized Scroll-Triggered Animations */
.fade-in-up {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, opacity;
    backface-visibility: hidden;
}

.fade-in-up.animate {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    will-change: auto;
}

.stagger-animation {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
    transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, opacity;
    backface-visibility: hidden;
}

.stagger-animation.animate {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    will-change: auto;
}

/* Optimized staggered delays */
.stagger-animation:nth-child(1) { transition-delay: 0.05s; }
.stagger-animation:nth-child(2) { transition-delay: 0.1s; }
.stagger-animation:nth-child(3) { transition-delay: 0.15s; }
.stagger-animation:nth-child(4) { transition-delay: 0.2s; }
.stagger-animation:nth-child(5) { transition-delay: 0.25s; }
.stagger-animation:nth-child(6) { transition-delay: 0.3s; }

/* Optimized Parallax Effects */
.parallax-element {
    transform: translate3d(0, 0, 0);
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Performance-optimized loading states */
.loading-skeleton {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
    transform: translate3d(0, 0, 0);
    will-change: background-position;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Scroll performance optimizations */
.scroll-optimized {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: auto;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .parallax-element {
        transform: none !important;
    }
}

/* Enhanced Focus States for Accessibility */
*:focus {
    outline: 2px solid #FFFFFF;
    outline-offset: 2px;
    border-radius: 4px;
}

button:focus, a:focus, input:focus, textarea:focus {
    box-shadow:
        0 0 0 3px rgba(255, 107, 53, 0.3),
        0 4px 16px rgba(255, 107, 53, 0.2);
}

/* Specific overrides for accent colors */
.coach-info h3 {
    color: var(--text-primary);
}

.amount, .currency {
    color: #ff6b35 !important;
}

.achievement i, .cert-item i, .feature-item i,
.guarantee-content i, .savings {
    color: #28a745 !important;
}

.coach-name-overlay p, .coach-badge i {
    color: #ffd700 !important;
}

/* Apple-inspired smooth scrolling */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }
}

/* 3D Animation Keyframes */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotateX(0deg); }
    50% { transform: translateY(-10px) rotateX(2deg); }
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-100px) rotateY(-15deg);
    }
    100% {
        opacity: 1;
        transform: translateX(0) rotateY(0deg);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100px) rotateY(15deg);
    }
    100% {
        opacity: 1;
        transform: translateX(0) rotateY(0deg);
    }
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(50px) rotateX(-15deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) rotateX(0deg);
    }
}

@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.8) rotateZ(-5deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotateZ(0deg);
    }
}

@keyframes pulse3D {
    0%, 100% {
        transform: scale(1) translateZ(0px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    }
    50% {
        transform: scale(1.05) translateZ(10px);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.6);
    }
}

@keyframes tilt {
    0%, 100% { transform: rotateY(0deg) rotateX(0deg); }
    25% { transform: rotateY(-2deg) rotateX(1deg); }
    75% { transform: rotateY(2deg) rotateX(-1deg); }
}

/* 3D Card Base Class */
.card-3d {
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
}

.card-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: inherit;
    z-index: -1;
    transform: translateZ(-1px);
}

.card-3d:hover {
    transform: translateY(-15px) rotateX(5deg) rotateY(5deg) translateZ(20px);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Luxury Navigation - Chanel Inspired */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--primary-white);
    z-index: 1000;
    padding: 1.5rem 0;
    box-shadow: 0 1px 0 var(--border-light);
    border-bottom: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

/* Luxury navbar styling */
.navbar .nav-logo h2 {
    font-family: 'Cinzel', 'Georgia', serif;
    color: var(--text-primary);
    font-weight: 400;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 1.5rem;
    margin: 0;
}

.navbar .nav-links a:not(.cta-nav) {
    font-family: 'Inter', sans-serif;
    color: var(--text-primary);
    font-weight: 300;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.navbar .cta-nav {
    color: var(--primary-white);
    background: var(--primary-black);
    font-weight: 400;
    border: 1px solid var(--primary-black);
}

/* Enhanced navbar on scroll */
.navbar.scrolled {
    background: var(--primary-white);
    box-shadow: 0 2px 8px var(--shadow-light);
    border-bottom: 1px solid var(--border-medium);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--text-primary);
    font-weight: 400;
    font-size: 1.5rem;
    margin: 0;
    letter-spacing: 0.1em;
}

.nav-links {
    display: flex;
    gap: 3rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 300;
    font-size: 0.85rem;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
    text-transform: uppercase;
}

.nav-links a:hover {
    color: var(--accent-orange);
    transform: translateY(-1px);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #FFFFFF, #E8E8E8);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(-50%);
    border-radius: 1px;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Active navigation state */
.nav-links a.active {
    color: var(--accent-orange);
}

.nav-links a.active::after {
    width: 100%;
    background: var(--accent-orange);
}

.cta-nav {
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    font-weight: 400;
    font-size: 0.85rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: 1px solid var(--primary-black);
}

.cta-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-nav:hover::before {
    left: 100%;
}

.cta-nav:hover {
    background: linear-gradient(135deg, #1a1a1a, #333333);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.6),
        0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #f5f5f7 !important;
    margin: 3px 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Luxury Hero Section - Chanel Inspired */
.hero {
    padding: 150px 0 100px;
    background: var(--primary-white);
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px solid var(--border-light);
}



.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    position: relative;
}

.hero-content {
    text-align: left;
}

.hero-headline {
    font-family: 'Cinzel', 'Georgia', serif;
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 3rem;
    letter-spacing: 0.1em;
    color: var(--text-primary);
    text-transform: uppercase;
}

.highlight {
    color: var(--text-primary);
    position: relative;
}

.hero-subheadline {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
    line-height: 1.8;
    font-weight: 300;
    letter-spacing: 0.02em;
    max-width: 500px;
}

.hero-benefits {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 4rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 300;
    font-size: 0.9rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.benefit-item i {
    color: var(--text-primary);
    font-size: 0.8rem;
}

.hero-cta {
    margin-top: 2rem;
}

.cta-primary {
    background: var(--primary-black);
    color: var(--primary-white);
    border: 1px solid var(--primary-black);
    padding: 1rem 2rem;
    font-size: 0.85rem;
    font-weight: 400;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    text-decoration: none;
}

.cta-primary:hover {
    background: var(--primary-white);
    color: var(--primary-black);
    border: 1px solid var(--primary-black);
}

.cta-subtext {
    margin-top: 1rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    animation: slideInRight 1s ease-out 0.3s both;
}

/* Luxury Hero Image Styles - Chanel Inspired */
.hero-image-container {
    width: 500px;
    height: 600px;
    position: relative;
    border-radius: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px var(--shadow-light);
}

.hero-image-container:hover {
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.hero-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
    transition: all 0.3s ease;
    filter: grayscale(20%);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    padding: 2rem;
    color: var(--primary-white);
}

.coach-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 0.5rem 1rem;
    border-radius: 0;
    font-weight: 300;
    font-size: 0.8rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.coach-badge i {
    color: var(--primary-white);
}

/* Luxury Section Titles - Chanel Style */
.section-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 300;
    text-align: center;
    margin-bottom: 4rem;
    color: var(--text-primary);
    letter-spacing: 0.1em;
    font-family: 'Cinzel', 'Georgia', serif;
    text-transform: uppercase;
}

/* Problem Section */
.problem {
    padding: 80px 0;
    background:
        linear-gradient(135deg, rgba(17, 17, 17, 0.95) 0%, rgba(0, 0, 0, 0.98) 100%),
        radial-gradient(circle at 30% 70%, rgba(255, 107, 53, 0.05) 0%, transparent 50%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.problem-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.problem-item {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-style: preserve-3d;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

.problem-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 107, 53, 0.1) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.problem-item:hover::before {
    opacity: 1;
}

.problem-item:hover {
    transform: translateY(-15px) rotateX(5deg) rotateY(5deg) translateZ(20px);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.problem-item i {
    font-size: 3rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.problem-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #f5f5f7;
    font-weight: 600;
    letter-spacing: -0.022em;
}

.problem-item p {
    color: rgba(245, 245, 247, 0.8);
    line-height: 1.6;
}

/* Luxury Solution Section - Chanel Style */
.solution {
    padding: 120px 0;
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

/* Solution Image Styles */
.solution-image-container {
    width: 100%;
    max-width: 500px;
    height: 400px;
    position: relative;
    border-radius: 25px;
    overflow: hidden;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.solution-image-container:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(5deg) translateZ(20px);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.solution-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 25px;
    transition: all 0.4s ease;
}

.transformation-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 215, 0, 0.95);
    color: #000;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.transformation-badge i {
    color: var(--text-primary);
}

.solution-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    color: #f5f5f7;
}

.solution-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: rgba(245, 245, 247, 0.8);
}

.solution-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature i {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-top: 0.25rem;
}

.feature h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #f5f5f7;
}

.solution-placeholder {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--text-primary);
}

.solution-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

/* Luxury Benefits Section - Chanel Style */
.benefits {
    padding: 120px 0;
    background: var(--primary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--primary-white);
    padding: 3rem 2rem;
    border-radius: 0;
    text-align: center;
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid var(--border-light);
}

.benefit-card:hover {
    box-shadow: 0 4px 16px var(--shadow-medium);
    transform: translateY(-5px);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-black);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    position: relative;
    transition: all 0.3s ease;
    box-shadow:
        0 10px 20px rgba(255, 107, 53, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.benefit-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #000000, #333333, #666666);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.benefit-card:hover .benefit-icon::before {
    opacity: 0.8;
}

.benefit-card:hover .benefit-icon {
    transform: translateZ(20px) rotateY(360deg);
    box-shadow:
        0 15px 30px rgba(255, 107, 53, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.benefit-icon i {
    font-size: 1.5rem;
    color: var(--primary-white);
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon i {
    transform: scale(1.1);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.benefit-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #f5f5f7;
    font-weight: 600;
    letter-spacing: -0.022em;
}

.benefit-card p {
    color: rgba(245, 245, 247, 0.8);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--primary-white);
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 3rem;
        gap: 1rem;
        transition: left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.4),
            inset 1px 0 0 rgba(255, 255, 255, 0.1);
        border-right: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-links a {
        padding: 1rem 2rem;
        margin: 0.5rem 0;
        width: 80%;
        text-align: center;
        font-size: 18px;
        font-weight: 500;
        position: relative;
    }

    .nav-links a:hover {
        color: var(--accent-orange);
        transform: translateX(5px);
    }

    .nav-links a::after {
        bottom: 0.5rem;
        left: 50%;
        transform: translateX(-50%);
    }

    .nav-links a:hover::after,
    .nav-links a.active::after {
        width: 60%;
    }

    .nav-links.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-headline {
        font-size: 2.5rem;
    }

    .hero-placeholder {
        width: 300px;
        height: 300px;
    }

    .solution-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .problem-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .stats-section {
        grid-template-columns: repeat(2, 1fr);
    }

    .certifications {
        grid-template-columns: 1fr;
    }

    .urgency-elements {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-primary.large {
        width: 100%;
        max-width: 300px;
    }

    .cta-secondary {
        width: 100%;
        max-width: 300px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2.5rem;
    }

    .quick-contact-form {
        padding: 2rem 1.5rem;
        margin-top: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-headline {
        font-size: 2rem;
    }

    .hero-subheadline {
        font-size: 1.1rem;
    }

    .hero-benefits {
        flex-direction: column;
        gap: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .pricing-card {
        padding: 2rem 1.5rem;
    }

    .price .amount {
        font-size: 3rem;
    }

    .stats-section {
        grid-template-columns: 1fr;
    }

    .stat-item h3 {
        font-size: 2.5rem;
    }
}

/* Luxury Testimonials Section - Chanel Style */
.testimonials {
    padding: 120px 0;
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.testimonial-card {
    background: var(--primary-white);
    padding: 3rem;
    border-radius: 0;
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid var(--border-light);
}

.testimonial-card:hover {
    box-shadow: 0 4px 16px var(--shadow-medium);
    transform: translateY(-5px);
}

.testimonial-details-btn {
    width: 100%;
    margin-top: 1.5rem;
    padding: 1rem 1.5rem;
    background: var(--primary-black);
    color: var(--primary-white);
    border: 1px solid var(--primary-black);
    border-radius: 0;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.testimonial-details-btn:hover {
    background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.testimonial-details-btn:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s ease;
}

.testimonial-details-btn i {
    transition: transform 0.3s ease;
}

.testimonial-details-btn:hover i {
    transform: translateX(3px);
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.stars i {
    color: #ffd700;
    font-size: 1.2rem;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: rgba(245, 245, 247, 0.9);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    box-shadow:
        0 5px 15px rgba(255, 107, 53, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.author-avatar:hover {
    transform: scale(1.1) translateZ(10px);
    box-shadow:
        0 10px 25px rgba(255, 107, 53, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.author-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.author-avatar:hover .author-image {
    transform: scale(1.05);
}

.author-info h4 {
    margin-bottom: 0.25rem;
    color: #f5f5f7;
    font-weight: 600;
    letter-spacing: -0.022em;
}

.author-info span {
    color: rgba(245, 245, 247, 0.7);
    font-size: 0.9rem;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item h3 {
    font-size: 3rem;
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 1.1rem;
    color: rgba(245, 245, 247, 0.7);
    font-weight: 500;
}

/* Luxury Client Results Section - Chanel Style */
.client-results {
    padding: 120px 0;
    background: var(--primary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
    scroll-margin-top: 80px; /* Ensure proper scroll positioning */
    transition: all 0.3s ease; /* Smooth transition for visual feedback */
}

.results-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.result-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    scroll-margin-top: 100px; /* Ensure proper scroll positioning for individual cards */
}

/* Highlighted client card effect */
.result-card.highlighted-client {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 0 4px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1.02) translateY(-5px);
    z-index: 10;
}

/* Pulse animation for highlighted client */
.result-card.highlighted-client::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.3) 100%);
    border-radius: 27px;
    z-index: -1;
    animation: highlightPulse 2s ease-in-out;
}

@keyframes highlightPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.result-card:hover::before {
    opacity: 1;
}

.result-card:hover {
    transform: translateY(-10px) rotateX(2deg) rotateY(2deg) translateZ(15px);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.result-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.client-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.client-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.client-avatar:hover .client-image {
    transform: scale(1.1);
}

.client-info h3 {
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    color: #FFFFFF !important;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.client-profession {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    display: block;
    margin-bottom: 0.75rem;
}

.transformation-period {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.transformation-period i {
    color: #FFFFFF;
}

.transformation-images {
    margin-bottom: 2rem;
}

.before-after-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.before-image, .after-image {
    text-align: center;
}

.image-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.image-placeholder.success {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-color: rgba(255, 255, 255, 0.4);
}

.image-placeholder i {
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.5rem;
}

.image-placeholder.success i {
    color: #FFFFFF;
}

.image-placeholder span {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    font-size: 0.9rem;
}

.image-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-weight: 500;
}

.transformation-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
}

.transformation-arrow i {
    font-size: 2rem;
    color: #FFFFFF;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.results-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.metric {
    text-align: center;
}

.metric-value {
    font-family: 'Oswald', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: #FFFFFF;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.metric-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
}

.transformation-story {
    background: rgba(255, 255, 255, 0.03);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.transformation-story h4 {
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    color: #FFFFFF !important;
    margin-bottom: 1rem;
    font-weight: 600;
}

.transformation-story p {
    color: rgba(255, 255, 255, 0.9) !important;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.program-highlights {
    margin-top: 1.5rem;
}

.program-highlights h5 {
    font-family: 'Oswald', sans-serif;
    font-size: 1.1rem;
    color: #FFFFFF !important;
    margin-bottom: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.program-highlights ul {
    list-style: none;
    padding: 0;
}

.program-highlights li {
    color: rgba(255, 255, 255, 0.8) !important;
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.5;
}

.program-highlights li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #FFFFFF;
    font-weight: bold;
    font-size: 1.1rem;
}

.results-cta {
    text-align: center;
    margin-top: 4rem;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.results-cta h3 {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    color: #FFFFFF !important;
    margin-bottom: 1rem;
    font-weight: 700;
}

.results-cta p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Luxury About Coach Section - Chanel Style */
.about-coach {
    padding: 120px 0;
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

/* Coach Image Styles */
.coach-image-container {
    width: 100%;
    max-width: 400px;
    height: 500px;
    position: relative;
    border-radius: 25px;
    overflow: hidden;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.coach-image-container:hover {
    transform: translateY(-15px) rotateX(8deg) rotateY(8deg) translateZ(30px);
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.coach-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 25px;
    transition: all 0.4s ease;
}

.coach-name-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 2rem;
    color: white;
    text-align: center;
}

.coach-name-overlay h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: white;
}

.coach-name-overlay p {
    color: #ffd700;
    font-weight: 600;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
    color: #f5f5f7;
}

.coach-placeholder {
    width: 300px;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    margin: 0 auto;
}

.coach-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.coach-info h3 {
    font-size: 2rem;
    color: #FFFFFF !important;
    margin-bottom: 1.5rem;
}

.certifications {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.08);
    padding: 1rem;
    border-radius: 10px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

.cert-item i {
    color: #FFFFFF;
    font-size: 1.2rem;
}

.cert-item span {
    color: #f5f5f7;
    font-weight: 500;
}

.coach-story {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: rgba(245, 245, 247, 0.9);
}

.coach-achievements {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.achievement {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 500;
    color: #f5f5f7;
}

.achievement i {
    color: #ffd700;
    font-size: 1.3rem;
}

/* Luxury Program Details Section - Chanel Style */
.program-details {
    padding: 120px 0;
    background: var(--primary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary);
}

.pricing-container {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.pricing-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 30px;
    padding: 3rem;
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.4),
        0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    max-width: 600px;
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
    z-index: -1;
}

.pricing-card:hover {
    transform: translateY(-20px) rotateX(8deg) rotateY(8deg) translateZ(40px);
    box-shadow:
        0 35px 80px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.pricing-card.featured {
    border: 3px solid #000000;
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.pricing-card.featured::before {
    content: "MOST POPULAR";
    position: absolute;
    top: 20px;
    right: -30px;
    background: #000000;
    color: white;
    padding: 0.5rem 2rem;
    font-size: 0.8rem;
    font-weight: 600;
    transform: rotate(45deg);
}

.pricing-header h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #f5f5f7;
    font-weight: 700;
    letter-spacing: -0.022em;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 1rem;
}

.currency {
    font-size: 1.5rem;
    color: #ff6b35;
    font-weight: 600;
}

.amount {
    font-size: 4rem;
    font-weight: 800;
    color: #ff6b35;
}

.period {
    font-size: 1.2rem;
    color: rgba(245, 245, 247, 0.7) !important;
    margin-left: 0.5rem;
}

.price-description {
    color: rgba(245, 245, 247, 0.8) !important;
    margin-bottom: 2rem;
}

.pricing-features {
    text-align: left;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item i {
    color: #28a745;
    font-size: 1.2rem;
    margin-top: 0.1rem;
}

.feature-item span {
    color: #f5f5f7;
    line-height: 1.5;
}

.value-proposition {
    background: rgba(40, 167, 69, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.value-proposition h4 {
    color: #f5f5f7;
    margin-bottom: 0.5rem;
}

.value-proposition p {
    color: rgba(245, 245, 247, 0.8);
}

.savings {
    color: #28a745;
    font-weight: 700;
    font-size: 1.2rem;
}

.pricing-cta {
    width: 100%;
    font-size: 1.2rem;
    padding: 1.5rem;
}

.guarantee {
    text-align: center;
    margin-top: 3rem;
}

.guarantee-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background: rgba(40, 167, 69, 0.1);
    padding: 2rem;
    border-radius: 15px;
    max-width: 600px;
    margin: 0 auto;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.guarantee-content i {
    font-size: 2rem;
    color: #28a745;
}

.guarantee-content h4 {
    color: #f5f5f7;
    margin-bottom: 0.5rem;
}

.guarantee-content p {
    color: rgba(245, 245, 247, 0.8);
    margin: 0;
}

/* FAQ Section */
.faq {
    padding: 80px 0;
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(17, 17, 17, 0.95) 100%),
        radial-gradient(circle at 40% 60%, rgba(255, 107, 53, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 60% 40%, rgba(120, 119, 198, 0.08) 0%, transparent 50%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    margin-bottom: 1rem;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: transparent;
    transition: background 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.faq-question:hover {
    background: rgba(255, 255, 255, 0.05);
}

.faq-question h4 {
    font-size: 1.2rem;
    color: #f5f5f7;
    margin: 0;
    font-weight: 600;
    letter-spacing: -0.022em;
}

.faq-question i {
    color: var(--text-primary);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 2rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 2rem 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: rgba(245, 245, 247, 0.8);
    line-height: 1.6;
}

/* Luxury Final CTA Section - Chanel Style */
.final-cta {
    padding: 120px 0;
    background: var(--secondary-white);
    color: var(--text-primary);
    text-align: center;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.cta-content h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
}

.cta-description {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.urgency-elements {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.urgency-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.urgency-item i {
    color: #ffd700;
    font-size: 1.3rem;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.cta-primary.large {
    font-size: 1.3rem;
    padding: 1.5rem 3rem;
}

.cta-secondary {
    background: rgba(255, 255, 255, 0.08);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    font-family: 'Oswald', sans-serif;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--primary-white);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cta-guarantee {
    font-size: 1rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Quick Contact Form */
.quick-contact-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    margin-top: 3rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-contact-form h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.contact-form input,
.contact-form select {
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.08);
    color: #f5f5f7;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.contact-form input:focus,
.contact-form select:focus {
    outline: none;
    background: var(--secondary-white);
    border-color: var(--border-medium);
    box-shadow: 0 0 0 3px var(--shadow-light);
}

/* Enhanced placeholder styling for luxury theme */
.contact-form input::placeholder {
    color: var(--text-tertiary);
    opacity: 0.8;
    font-weight: 300;
}

.contact-form input::-webkit-input-placeholder {
    color: var(--text-tertiary);
    opacity: 0.8;
    font-weight: 300;
}

.contact-form input::-moz-placeholder {
    color: var(--text-tertiary);
    opacity: 0.8;
    font-weight: 300;
}

.contact-form input:-ms-input-placeholder {
    color: var(--text-tertiary);
    opacity: 0.8;
    font-weight: 300;
}

.form-submit-btn {
    background: var(--primary-black);
    color: var(--primary-white);
    border: 1px solid var(--primary-black);
    padding: 1.25rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.form-submit-btn:hover {
    background: #1a1a1a;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.form-disclaimer {
    text-align: center;
    font-size: 0.9rem;
    margin-top: 1rem;
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Luxury Footer - Chanel Style */
.footer {
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 80px 0 40px;
    border-top: 1px solid var(--border-medium);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 2rem;
    color: var(--primary-white);
    font-family: 'Cinzel', serif;
    font-weight: 300;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 1rem;
}

.footer-section p {
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-white);
    color: var(--primary-black);
    border-color: var(--primary-white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-white);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-item i {
    color: var(--primary-white);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-white);
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Loading Animations */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Scroll-triggered animations */
.animate-in {
    opacity: 1 !important;
}

/* Enhanced card animations */
.card-enter {
    animation: cardEnter 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes cardEnter {
    0% {
        opacity: 0;
        transform: translateY(50px) rotateX(-15deg) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) rotateX(0deg) scale(1);
    }
}

/* Staggered grid animations */
.grid-item-1 { animation-delay: 0.1s; }
.grid-item-2 { animation-delay: 0.2s; }
.grid-item-3 { animation-delay: 0.3s; }
.grid-item-4 { animation-delay: 0.4s; }
.grid-item-5 { animation-delay: 0.5s; }
.grid-item-6 { animation-delay: 0.6s; }

/* Page transition effects */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Optimized scroll indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #000000, #333333);
    z-index: 9999;
    transform: scaleX(0);
    transform-origin: left;
    will-change: transform;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Particle Effects */
.particle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

@keyframes floatUp {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Cursor Trail */
.cursor-trail {
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: screen;
}

/* Enhanced Button Interactions */
.cta-primary, .cta-secondary {
    position: relative;
    overflow: hidden;
    will-change: transform;
}

.cta-primary::after, .cta-secondary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.cta-primary:hover::after, .cta-secondary:hover::after {
    width: 300px;
    height: 300px;
}

/* Magnetic Button Effect */
.magnetic-button {
    transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Section Reveal Animations */
.section-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.section-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--primary-black);
    color: var(--primary-white);
    border: 1px solid var(--primary-black);
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.5),
        0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.6),
        0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.scroll-to-top:active {
    transform: translateY(-1px) scale(1.02);
}

/* Enhanced Card Hover States */
.interactive-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    will-change: transform;
}

.interactive-card:hover {
    transform: translateY(-20px) rotateX(10deg) rotateY(10deg) translateZ(40px);
}

/* Glowing Elements */
.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #000000, #333333, #666666, #999999);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    filter: blur(10px);
}

.glow-effect:hover::before {
    opacity: 0.7;
    animation: glow-pulse 2s ease-in-out infinite;
}

@keyframes glow-pulse {
    0%, 100% { filter: blur(10px); }
    50% { filter: blur(15px); }
}

/* Performance Optimizations */
:root {
    --animation-duration: 0.6s;
    --transition-duration: 0.4s;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    :root {
        --animation-duration: 0.01s;
        --transition-duration: 0.01s;
    }

    * {
        animation-duration: var(--animation-duration) !important;
        transition-duration: var(--transition-duration) !important;
        transform: none !important;
    }

    .particle-container,
    .cursor-trail {
        display: none !important;
    }
}

/* Client Results Mobile Responsive */
@media (max-width: 768px) {
    .client-results {
        padding: 60px 0;
    }

    .results-grid {
        gap: 2rem;
        margin-top: 2rem;
    }

    .result-card {
        padding: 1.5rem;
        border-radius: 20px;
    }

    .result-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .client-avatar {
        width: 70px;
        height: 70px;
    }

    .client-info h3 {
        font-size: 1.3rem;
    }

    .before-after-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .transformation-arrow {
        transform: rotate(90deg);
    }

    .image-placeholder {
        width: 120px;
        height: 120px;
    }

    .results-metrics {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .transformation-story {
        padding: 1.5rem;
    }

    .transformation-story h4 {
        font-size: 1.2rem;
    }

    .results-cta {
        margin-top: 3rem;
        padding: 2rem 1.5rem;
    }

    .results-cta h3 {
        font-size: 1.5rem;
    }

    .testimonial-details-btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
    /* Optimize background images for mobile performance */
    .hero, .solution, .benefits, .about-coach, .program-details, .final-cta, .client-results {
        background-attachment: scroll !important;
        background-size: cover !important;
    }

    /* Reduce 3D effects on mobile for better performance */
    .card-3d:hover {
        transform: translateY(-10px) scale(1.02) !important;
    }

    .hero-image-container:hover, .solution-image-container:hover, .coach-image-container:hover {
        transform: translateY(-10px) scale(1.02) !important;
    }

    /* Optimize animations for mobile */
    .benefit-card:hover, .testimonial-card:hover, .problem-item:hover {
        transform: translateY(-10px) scale(1.02) !important;
    }

    .pricing-card:hover {
        transform: translateY(-15px) scale(1.02) !important;
    }

    /* Disable particle effects on mobile */
    .particle-container {
        display: none !important;
    }

    /* Disable cursor trail on mobile */
    .cursor-trail {
        display: none !important;
    }

    /* Simplify button effects on mobile */
    .cta-primary:hover, .cta-secondary:hover {
        transform: translateY(-3px) scale(1.05) !important;
    }

    /* Reduce animation complexity */
    * {
        animation-duration: var(--animation-duration) !important;
        transition-duration: var(--transition-duration) !important;
    }

    /* Optimize will-change usage */
    .card-3d, .benefit-card, .testimonial-card, .problem-item {
        will-change: auto !important;
    }
}

/* High performance mode for low-end devices */
@media (max-width: 480px) {
    /* Disable all 3D transforms */
    .card-3d:hover,
    .benefit-card:hover,
    .testimonial-card:hover,
    .problem-item:hover,
    .pricing-card:hover,
    .hero-image-container:hover,
    .solution-image-container:hover,
    .coach-image-container:hover {
        transform: translateY(-5px) !important;
    }

    /* Disable complex animations */
    @keyframes float {
        from, to { transform: none; }
    }

    @keyframes tilt {
        from, to { transform: none; }
    }

    @keyframes pulse3D {
        from, to { transform: none; }
    }
}

/* Improved Visual Hierarchy and Spacing for Streamlined Page */
.hero + .solution {
    padding-top: 80px;
}

.solution + .benefits {
    padding-top: 60px;
}

.testimonials + .client-results {
    padding-top: 60px;
}

.about-coach + .program-details {
    padding-top: 60px;
}

.program-details + .faq {
    padding-top: 60px;
}

/* Improved section spacing for better flow */
.hero,
.solution,
.benefits,
.testimonials,
.client-results,
.about-coach,
.program-details,
.faq,
.final-cta {
    padding: 100px 0;
}

/* Reduce testimonials grid gap for more compact layout */
.testimonials-grid {
    gap: 2rem;
}

/* Improve results grid spacing */
.results-grid {
    gap: 2.5rem;
}

/* Simplified Key Benefits Styling */
.key-benefits {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.benefit-item i {
    color: #FF6B35;
    font-size: 1rem;
    flex-shrink: 0;
}

.benefit-item span {
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 300;
}

/* Adjust transformation story spacing */
.transformation-story h4 {
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.transformation-story p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Luxury Instagram Follow Section - Chanel Style */
.instagram-follow {
    background: var(--secondary-white);
    padding: 100px 0;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.instagram-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 40px;
}

.instagram-icon {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    background: var(--primary-black);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.instagram-icon i {
    font-size: 1.5rem;
    color: var(--primary-white);
}

.instagram-text {
    flex: 1;
    text-align: center;
}

.instagram-text h3 {
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.instagram-text p {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-weight: 300;
}

.instagram-handle {
    font-family: 'Inter', sans-serif;
    font-size: 0.8rem;
    font-weight: 400;
    color: var(--text-tertiary);
    background: transparent;
    padding: 0.5rem 0;
    border: none;
    display: inline-block;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.instagram-follow-btn {
    flex-shrink: 0;
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    text-decoration: none;
    font-family: 'Inter', sans-serif;
    font-size: 0.8rem;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid var(--primary-black);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    letter-spacing: 0.5px;
}

.instagram-follow-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.instagram-follow-btn:hover::before {
    left: 100%;
}

.instagram-follow-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 10px 30px rgba(225, 48, 108, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.instagram-follow-btn i {
    font-size: 1.3rem;
}

/* Enhanced Instagram link in footer */
.social-links .instagram-link {
    background: linear-gradient(45deg, #E1306C, #F56040);
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.social-links .instagram-link::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #E1306C, #F56040, #FCAF45);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-links .instagram-link:hover::before {
    opacity: 0.7;
}

.social-links .instagram-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(225, 48, 108, 0.4);
}

/* Instagram animations */
@keyframes instagramPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes instagramGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

/* Responsive Instagram Section */
@media (max-width: 768px) {
    .instagram-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .instagram-text h3 {
        font-size: 1.8rem;
    }

    .instagram-follow-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

/* Luxury YouTube Channel Section - Chanel Style */
.youtube-channel {
    background: var(--primary-white);
    padding: 100px 0;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.youtube-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.youtube-icon {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #FF0000, #CC0000);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: youtubePulse 3s ease-in-out infinite;
}

.youtube-icon::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(45deg, #FF0000, #CC0000, #FF4444);
    border-radius: 23px;
    z-index: -1;
    opacity: 0.3;
    animation: youtubeGlow 2s ease-in-out infinite alternate;
}

.youtube-icon i {
    font-size: 1.5rem;
    color: var(--primary-white);
}

.youtube-text {
    flex: 1;
    text-align: center;
}

.youtube-text h3 {
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.youtube-text p {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.youtube-handle {
    font-family: 'Oswald', sans-serif;
    font-size: 1.3rem;
    font-weight: 500;
    color: #FF0000;
    background: rgba(255, 0, 0, 0.1);
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    border: 1px solid rgba(255, 0, 0, 0.3);
    display: inline-block;
    letter-spacing: 0.5px;
}

.youtube-subscribe-btn {
    flex-shrink: 0;
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    text-decoration: none;
    font-family: 'Oswald', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.youtube-subscribe-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.youtube-subscribe-btn:hover::before {
    left: 100%;
}

.youtube-subscribe-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 10px 30px rgba(255, 0, 0, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.youtube-subscribe-btn i {
    font-size: 1.3rem;
}

/* Enhanced YouTube link in footer */
.social-links .youtube-link {
    background: linear-gradient(45deg, #FF0000, #CC0000);
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.social-links .youtube-link::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #FF0000, #CC0000, #FF4444);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-links .youtube-link:hover::before {
    opacity: 0.7;
}

.social-links .youtube-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(255, 0, 0, 0.4);
}

/* YouTube animations */
@keyframes youtubePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes youtubeGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

/* Responsive YouTube Section */
@media (max-width: 768px) {
    .youtube-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .youtube-text h3 {
        font-size: 1.8rem;
    }

    .youtube-subscribe-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}
