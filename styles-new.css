/* Lu<PERSON><PERSON>-Inspired Color Scheme */
:root {
    --primary-white: #ffffff;
    --secondary-white: #fafafa;
    --tertiary-white: #f5f5f5;
    --primary-black: #000000;
    --secondary-black: #1a1a1a;
    --tertiary-black: #333333;
    --text-primary: #000000;
    --text-secondary: #333333;
    --text-tertiary: #666666;
    --border-light: #e5e5e5;
    --border-medium: #cccccc;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.2);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--primary-white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-weight: 300;
    letter-spacing: 0.01em;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cinzel', 'Georgia', serif;
    color: var(--text-primary);
    font-weight: 400;
    line-height: 1.3;
    letter-spacing: 0.05em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
}

h1 {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 300;
    letter-spacing: 0.1em;
    margin-bottom: 2rem;
}

h2 {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 300;
    letter-spacing: 0.08em;
    margin-bottom: 2rem;
}

h3 {
    font-size: clamp(1.2rem, 2.5vw, 1.5rem);
    font-weight: 400;
    letter-spacing: 0.06em;
    margin-bottom: 1rem;
}

p {
    color: var(--text-secondary);
    font-family: 'Inter', sans-serif;
    font-weight: 300;
    line-height: 1.8;
    letter-spacing: 0.02em;
    margin-bottom: 1rem;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--primary-white);
    z-index: 1000;
    padding: 1.5rem 0;
    box-shadow: 0 1px 0 var(--border-light);
    border-bottom: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-black);
    font-weight: 600;
    font-size: 1.8rem;
    margin: 0;
    letter-spacing: 0.15em;
    font-family: 'Cinzel', serif;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.nav-logo h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-black) 0%, transparent 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.nav-logo:hover h2 {
    transform: translateY(-1px);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.nav-logo:hover h2::after {
    transform: scaleX(1);
}

.nav-logo {
    padding: 0.5rem 1rem;
    border-radius: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-logo:hover {
    background: rgba(0, 0, 0, 0.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-links {
    display: flex;
    gap: 3rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 300;
    font-size: 0.85rem;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
    text-transform: uppercase;
}

.nav-links a:hover {
    color: var(--text-secondary);
}

.cta-nav {
    background: var(--primary-black) !important;
    color: var(--primary-white) !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0;
    font-weight: 400;
    border: 1px solid var(--primary-black);
}

.cta-nav:hover {
    background: var(--primary-white) !important;
    color: var(--primary-black) !important;
    border: 1px solid var(--primary-black);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 2px;
    background: var(--primary-black);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 150px 0 100px;
    background: var(--primary-white);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.hero-content {
    text-align: left;
}

.hero-headline {
    font-family: 'Cinzel', serif;
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 2rem;
    letter-spacing: 0.1em;
    color: var(--text-primary);
    text-transform: uppercase;
}

.hero-headline strong {
    font-weight: 700;
    color: var(--primary-black);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    letter-spacing: 0.05em;
    position: relative;
}

.hero-subheadline {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
    line-height: 1.8;
    font-weight: 300;
    letter-spacing: 0.02em;
    max-width: 500px;
}

.hero-benefits {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 3rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 300;
    font-size: 0.9rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.benefit-item i {
    color: var(--text-primary);
    font-size: 0.8rem;
}

.hero-cta {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.cta-primary {
    background: var(--primary-black);
    color: var(--primary-white);
    border: 1px solid var(--primary-black);
    padding: 1rem 2rem;
    font-size: 0.85rem;
    font-weight: 400;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.cta-primary:hover {
    background: var(--primary-white);
    color: var(--primary-black);
    border: 1px solid var(--primary-black);
}

.cta-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
    padding: 1rem 2rem;
    font-size: 0.85rem;
    font-weight: 400;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.cta-secondary:hover {
    background: var(--secondary-white);
    border-color: var(--border-medium);
}

/* WhatsApp Button Styling */
.cta-secondary[href*="wa.me"] {
    background: var(--primary-black);
    color: var(--primary-white);
    border: 1px solid var(--primary-black);
    font-weight: 500;
}

.cta-secondary[href*="wa.me"]:hover {
    background: var(--primary-white);
    border: 1px solid var(--primary-black);
    color: var(--primary-black);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.cta-secondary[href*="wa.me"] i {
    color: var(--primary-white);
    font-size: 1rem;
    transition: color 0.3s ease;
}

.cta-secondary[href*="wa.me"]:hover i {
    color: var(--primary-black);
}

/* Hero Image */
.hero-image-container {
    width: 100%;
    height: 600px;
    position: relative;
    border-radius: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px var(--shadow-light);
}

.hero-image-container:hover {
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.hero-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
    transition: all 0.3s ease;
    filter: grayscale(20%);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    padding: 2rem;
    color: var(--primary-white);
}

.coach-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 0.5rem 1rem;
    border-radius: 0;
    font-weight: 300;
    font-size: 0.8rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.coach-badge i {
    color: var(--primary-white);
}

/* Section Styling */
section {
    padding: 120px 0;
}

.section-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 300;
    text-align: center;
    margin-bottom: 4rem;
    color: var(--text-primary);
    letter-spacing: 0.1em;
    font-family: 'Cinzel', serif;
    text-transform: uppercase;
}

/* Benefits Section */
.benefits {
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
}

.benefit-card {
    background: var(--primary-white);
    padding: 3rem 2rem;
    border-radius: 0;
    text-align: center;
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.benefit-card:hover {
    box-shadow: 0 4px 16px var(--shadow-medium);
    transform: translateY(-5px);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-black);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all 0.3s ease;
}

.benefit-icon i {
    font-size: 1.5rem;
    color: var(--primary-white);
}

.benefit-card h3 {
    color: var(--text-primary);
    font-family: 'Cinzel', serif;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 1rem;
}

.benefit-card p {
    color: var(--text-secondary);
    font-family: 'Inter', sans-serif;
    font-weight: 300;
    line-height: 1.6;
}

/* About Section */
.about {
    background: var(--primary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.about-text h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-weight: 300;
}

.credentials {
    margin: 3rem 0;
}

.credentials h4 {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.credentials ul {
    list-style: none;
    padding: 0;
}

.credentials li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.credentials i {
    color: var(--text-primary);
    font-size: 0.8rem;
}

.philosophy {
    margin-top: 3rem;
}

.philosophy h4 {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced About Section - Circular Profile with Background */
.coach-photo-container {
    position: relative;
    width: 100%;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 0;
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.workout-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: grayscale(30%) brightness(0.7);
    transition: all 0.3s ease;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(0, 0, 0, 0.3) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    z-index: 2;
}

.circular-profile {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.coach-headshot {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    object-position: center top;
    border: 6px solid var(--primary-white);
    box-shadow:
        0 0 0 2px var(--border-light),
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    filter: brightness(1.1) contrast(1.1);
}

.profile-border {
    position: absolute;
    top: -8px;
    left: -8px;
    width: calc(100% + 16px);
    height: calc(100% + 16px);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    pointer-events: none;
    animation: pulse-border 3s ease-in-out infinite;
}

@keyframes pulse-border {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.3;
    }
}

.coach-credential-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    font-weight: 300;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 4;
    transition: all 0.3s ease;
}

.coach-credential-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.coach-credential-badge i {
    color: var(--primary-white);
    font-size: 0.9rem;
}

/* Hover effects for the entire photo container */
.coach-photo-container:hover .background-image {
    filter: grayscale(20%) brightness(0.8);
    transform: scale(1.02);
}

.coach-photo-container:hover .coach-headshot {
    transform: scale(1.05);
    box-shadow:
        0 0 0 2px var(--border-light),
        0 12px 35px rgba(0, 0, 0, 0.4),
        0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Testimonials Section */
.testimonials {
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.testimonial-card {
    background: var(--primary-white);
    padding: 3rem;
    border-radius: 0;
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.testimonial-card:hover {
    box-shadow: 0 4px 16px var(--shadow-medium);
    transform: translateY(-5px);
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.stars i {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 2rem;
    line-height: 1.8;
    color: var(--text-secondary);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    text-transform: none;
    letter-spacing: 0.02em;
}

.author-info span {
    font-size: 0.8rem;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Program Section */
.program {
    background: var(--primary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.program-overview {
    text-align: center;
    margin-bottom: 4rem;
}

.program-overview h3 {
    font-size: 1.8rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.program-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.feature-item {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-black);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    color: var(--primary-white);
    font-size: 1.5rem;
}

.feature-content h4 {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.feature-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.pricing-grid {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.pricing-card {
    background: var(--primary-white);
    border: 1px solid var(--border-light);
    border-radius: 0;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 4px 20px var(--shadow-light);
    max-width: 400px;
    width: 100%;
}

.pricing-card.featured {
    border: 2px solid var(--primary-black);
    transform: scale(1.05);
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 3rem;
}

.currency {
    font-size: 1.5rem;
    color: var(--text-secondary);
}

.amount {
    font-size: 3rem;
    font-weight: 300;
    color: var(--text-primary);
    font-family: 'Cinzel', serif;
}

.period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.pricing-features ul {
    list-style: none;
    padding: 0;
    margin-bottom: 3rem;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.pricing-features i {
    color: var(--text-primary);
    font-size: 0.8rem;
}

.pricing-guarantee {
    text-align: center;
    margin-top: 3rem;
}

.pricing-guarantee p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.pricing-guarantee i {
    color: var(--text-primary);
}

/* FAQ Section */
.faq {
    background: var(--primary-white);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 1rem;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    color: var(--text-secondary);
}

.faq-question h3 {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 400;
    color: var(--text-primary);
    margin: 0;
    text-transform: none;
    letter-spacing: 0.02em;
}

.faq-question i {
    color: var(--text-primary);
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding-bottom: 2rem;
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Final CTA Section */
.final-cta {
    background: var(--secondary-white);
    border-top: 1px solid var(--border-light);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.cta-content > p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-bottom: 4rem;
}

.contact-info {
    margin-top: 4rem;
}

.contact-info > p {
    font-size: 1rem;
    color: var(--primary-black);
    margin-bottom: 2rem;
    font-weight: 500;
}

.contact-methods {
    display: flex;
    gap: 3rem;
    justify-content: center;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-black);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.contact-item:hover {
    color: var(--text-primary);
}

.contact-item i {
    font-size: 0.8rem;
    color: var(--primary-black);
}

.contact-item span {
    color: var(--primary-black);
    font-weight: 500;
}

/* About Page Specific Styles */
.about-hero {
    background: var(--primary-white);
    padding: 120px 0 80px;
    border-bottom: 1px solid var(--border-light);
}

.about-hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.about-hero-title {
    font-family: 'Cinzel', serif;
    font-size: 3rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
}

.about-hero-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    font-weight: 300;
}

.about-hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

.stat-item:hover {
    box-shadow: 0 4px 15px var(--shadow-light);
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.about-profile {
    padding: 80px 0;
    background: var(--secondary-white);
}

.training-philosophy {
    padding: 80px 0;
    background: var(--primary-white);
}

.philosophy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.philosophy-card {
    background: var(--secondary-white);
    padding: 3rem;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    text-align: center;
}

.philosophy-card:hover {
    box-shadow: 0 8px 25px var(--shadow-medium);
    transform: translateY(-10px);
}

.philosophy-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-black);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all 0.3s ease;
}

.philosophy-icon i {
    font-size: 2rem;
    color: var(--primary-white);
}

.philosophy-card:hover .philosophy-icon {
    transform: scale(1.1);
}

.philosophy-card h3 {
    font-family: 'Inter', sans-serif;
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.philosophy-card p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.expertise-areas {
    padding: 80px 0;
    background: var(--secondary-white);
}

.expertise-content {
    margin-top: 3rem;
}

.expertise-item {
    margin-bottom: 3rem;
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-light);
}

.expertise-item:last-child {
    border-bottom: none;
}

.expertise-item h3 {
    font-family: 'Inter', sans-serif;
    font-size: 1.3rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.expertise-item h3 i {
    color: var(--text-primary);
    font-size: 1.1rem;
}

.expertise-item p {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-left: 2.1rem;
}

.success-preview {
    padding: 80px 0;
    background: var(--primary-white);
    text-align: center;
}

.success-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.success-stat {
    padding: 2rem;
    border: 2px solid var(--border-light);
    transition: all 0.3s ease;
}

.success-stat:hover {
    border-color: var(--text-primary);
    transform: translateY(-5px);
}

.success-number {
    display: block;
    font-family: 'Cinzel', serif;
    font-size: 3rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.success-label {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.success-cta {
    margin-top: 4rem;
}

.success-cta p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Navigation active state for About page */
.nav-links a.active {
    color: var(--text-primary);
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 80px 0 40px;
    border-top: 1px solid var(--border-medium);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 2rem;
    color: var(--primary-white);
    font-family: 'Cinzel', serif;
    font-weight: 300;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 1rem;
}

.footer-section p {
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-white);
    color: var(--primary-black);
    border-color: var(--primary-white);
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.9rem;
}

.footer-section ul li a:hover {
    color: var(--primary-white);
}

.footer .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--primary-white);
    font-size: 0.9rem;
    font-weight: 400;
}

.footer .contact-item i {
    color: var(--primary-white);
    width: 20px;
    font-size: 1rem;
}

.footer .contact-item span {
    color: var(--primary-white);
    font-weight: 400;
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: var(--primary-white);
    border-radius: 0;
    font-size: 0.9rem;
}

.newsletter-form input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-btn {
    background: var(--primary-white);
    color: var(--primary-black);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    font-size: 0.8rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-btn:hover {
    background: rgba(255, 255, 255, 0.9);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.8rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-white);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }

    .nav-container {
        padding: 0 20px;
    }

    .nav-links {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--primary-white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 3rem;
        transition: left 0.3s ease;
        box-shadow: 0 2px 10px var(--shadow-light);
    }

    .nav-links.active {
        left: 0;
    }

    .nav-links a {
        margin: 1rem 0;
        font-size: 1.1rem;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .hero {
        padding: 120px 0 80px;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-image {
        order: -1;
    }

    .hero-image-container {
        height: 400px;
    }

    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    /* Mobile responsive for enhanced photo section */
    .coach-photo-container {
        height: 400px;
    }

    .coach-headshot {
        width: 150px;
        height: 150px;
        border-width: 4px;
    }

    .profile-border {
        top: -6px;
        left: -6px;
        width: calc(100% + 12px);
        height: calc(100% + 12px);
    }

    .coach-credential-badge {
        bottom: 15px;
        right: 15px;
        padding: 0.5rem 1rem;
        font-size: 0.7rem;
    }

    .coach-credential-badge i {
        font-size: 0.8rem;
    }

    /* About page mobile responsive */
    .about-hero-title {
        font-size: 2rem;
    }

    .about-hero-stats {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .philosophy-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .philosophy-card {
        padding: 2rem;
    }

    .expertise-item h3 {
        font-size: 1.1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .expertise-item p {
        margin-left: 0;
    }

    .success-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .success-number {
        font-size: 2rem;
    }

    /* Enhanced logo mobile responsive */
    .nav-logo h2 {
        font-size: 1.4rem;
        letter-spacing: 0.1em;
    }

    .nav-logo {
        padding: 0.3rem 0.5rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .program-features {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .contact-methods {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }

    .newsletter-form {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .hero-headline {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .benefit-card,
    .testimonial-card {
        padding: 2rem 1.5rem;
    }

    .pricing-card {
        padding: 2rem;
    }

    .amount {
        font-size: 2.5rem;
    }

    .social-links {
        justify-content: center;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus states for accessibility */
a:focus,
button:focus,
input:focus {
    outline: 2px solid var(--text-primary);
    outline-offset: 2px;
}

/* Loading animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content,
.benefit-card,
.testimonial-card,
.feature-item {
    animation: fadeInUp 0.6s ease-out;
}
