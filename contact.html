<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Coach <PERSON><PERSON><PERSON>dav | Start Your Fitness Transformation</title>
    <meta name="description" content="Get in touch with Coach <PERSON><PERSON><PERSON> Yadav to start your fitness transformation. Book consultation, ask questions, and begin your journey to better health.">

    <!-- Performance and SEO Meta Tags -->
    <meta name="keywords" content="contact fitness coach, book consultation, fitness coaching Mumbai, personal trainer contact, Netrap<PERSON> Singh Yadav">
    <meta name="author" content="Netrapal Singh Yadav">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://fitcoachpro.com/contact">

    <!-- Open Graph Meta Tags for Social Sharing -->
    <meta property="og:title" content="Contact Coach Netrapal Singh Yadav | Start Your Fitness Transformation">
    <meta property="og:description" content="Get in touch to start your fitness transformation journey with expert coaching and personalized guidance.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://fitcoachpro.com/contact">
    <meta property="og:image" content="https://fitcoachpro.com/og-image.jpg">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact Coach Netrapal Singh Yadav">
    <meta name="twitter:description" content="Start your fitness transformation journey with expert coaching.">
    <meta name="twitter:image" content="https://fitcoachpro.com/twitter-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800;900&family=Oswald:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800;900&display=swap" as="style">
    <link rel="preload" href="styles.css" as="style">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800;900&family=Oswald:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "name": "Contact Coach Netrapal Singh Yadav",
        "description": "Get in touch to start your fitness transformation journey",
        "url": "https://fitcoachpro.com/contact",
        "mainEntity": {
            "@type": "Person",
            "name": "Netrapal Singh Yadav",
            "jobTitle": "Fitness Coach & Nutrition Specialist",
            "telephone": "+91-98765-43210",
            "email": "<EMAIL>"
        }
    }
    </script>
</head>
<body>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator" id="scrollIndicator"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>Coach Natru</h2>
            </div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About Coach</a>
                <a href="results.html">Results</a>
                <a href="contact.html" class="active">Contact</a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Contact Hero Section -->
    <section class="contact-hero">
        <div class="container">
            <div class="contact-hero-content">
                <h1 class="hero-headline">
                    Ready to <span class="highlight">Transform Your Life?</span>
                </h1>
                <p class="hero-subheadline">
                    Take the first step towards your fitness goals. Get in touch to start your personalized transformation journey.
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="contact-form-section">
        <div class="container">
            <div class="contact-content">
                <div class="contact-info">
                    <h2>Get Started Today</h2>
                    <p>Ready to transform your body and life? Fill out the form and I'll get back to you within 24 hours to discuss your goals and how we can achieve them together.</p>
                    
                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="method-info">
                                <h3>Phone</h3>
                                <p>+91 98765 43210</p>
                                <span>Available 9 AM - 8 PM</span>
                            </div>
                        </div>
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="method-info">
                                <h3>Email</h3>
                                <p><EMAIL></p>
                                <span>Response within 24 hours</span>
                            </div>
                        </div>
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="method-info">
                                <h3>WhatsApp</h3>
                                <p>+91 98765 43210</p>
                                <span>Quick responses</span>
                            </div>
                        </div>
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="method-info">
                                <h3>Location</h3>
                                <p>Mumbai, India</p>
                                <span>In-person & online sessions</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="contact-form">
                    <form id="contactForm" class="consultation-form">
                        <h3>Book Your Free Consultation</h3>
                        
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="age">Age</label>
                            <input type="number" id="age" name="age" min="16" max="80">
                        </div>
                        
                        <div class="form-group">
                            <label for="goals">Primary Fitness Goals *</label>
                            <select id="goals" name="goals" required>
                                <option value="">Select your primary goal</option>
                                <option value="weight-loss">Weight Loss</option>
                                <option value="muscle-building">Muscle Building</option>
                                <option value="strength-training">Strength Training</option>
                                <option value="body-recomposition">Body Recomposition</option>
                                <option value="general-fitness">General Fitness</option>
                                <option value="athletic-performance">Athletic Performance</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="experience">Fitness Experience Level</label>
                            <select id="experience" name="experience">
                                <option value="">Select your experience level</option>
                                <option value="beginner">Beginner (0-6 months)</option>
                                <option value="intermediate">Intermediate (6 months - 2 years)</option>
                                <option value="advanced">Advanced (2+ years)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="availability">Preferred Training Time</label>
                            <select id="availability" name="availability">
                                <option value="">Select preferred time</option>
                                <option value="morning">Morning (6 AM - 10 AM)</option>
                                <option value="afternoon">Afternoon (12 PM - 4 PM)</option>
                                <option value="evening">Evening (5 PM - 8 PM)</option>
                                <option value="flexible">Flexible</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Tell me about your goals and any specific requirements *</label>
                            <textarea id="message" name="message" rows="4" placeholder="Describe your fitness goals, any injuries or limitations, and what you hope to achieve..." required></textarea>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="newsletter" name="newsletter">
                                <span class="checkmark"></span>
                                I'd like to receive fitness tips and updates via email
                            </label>
                        </div>
                        
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-paper-plane"></i>
                            Send Message & Book Consultation
                        </button>
                        
                        <p class="form-note">* Required fields. Your information is kept confidential and never shared.</p>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="process-section">
        <div class="container">
            <h2 class="section-title">How It Works</h2>
            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Free Consultation</h3>
                        <p>We'll discuss your goals, assess your current fitness level, and create a personalized plan.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Program Design</h3>
                        <p>I'll create your custom training and nutrition program based on your specific needs and preferences.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Start Training</h3>
                        <p>Begin your transformation with 24 sessions per month and complete support system.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Achieve Results</h3>
                        <p>Watch your body transform as you build strength, lose fat, and develop lasting healthy habits.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <div class="faq-grid">
                <div class="faq-item">
                    <h3>How much does the program cost?</h3>
                    <p>The complete transformation program is $99 per month, which includes 24 one-hour personal training sessions, nutrition coaching, and ongoing support.</p>
                </div>
                <div class="faq-item">
                    <h3>Do you offer online coaching?</h3>
                    <p>Yes! I offer both in-person sessions in Mumbai and online coaching for clients anywhere in the world.</p>
                </div>
                <div class="faq-item">
                    <h3>What if I'm a complete beginner?</h3>
                    <p>Perfect! I specialize in working with beginners and will guide you through every step of your fitness journey safely and effectively.</p>
                </div>
                <div class="faq-item">
                    <h3>How quickly will I see results?</h3>
                    <p>Most clients see noticeable changes within 2-3 weeks, with significant transformations typically occurring within 12-16 weeks.</p>
                </div>
                <div class="faq-item">
                    <h3>Do you provide meal plans?</h3>
                    <p>Yes, I provide personalized nutrition guidance including meal planning, macro tracking, and flexible dieting strategies.</p>
                </div>
                <div class="faq-item">
                    <h3>What if I have injuries or limitations?</h3>
                    <p>I work with clients of all fitness levels and can modify programs to accommodate injuries or physical limitations safely.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof -->
    <section class="social-proof">
        <div class="container">
            <h2 class="section-title">Join 500+ Successful Clients</h2>
            <div class="social-stats">
                <div class="social-stat">
                    <i class="fas fa-users"></i>
                    <h3>500+</h3>
                    <p>Clients Transformed</p>
                </div>
                <div class="social-stat">
                    <i class="fas fa-star"></i>
                    <h3>4.9/5</h3>
                    <p>Average Rating</p>
                </div>
                <div class="social-stat">
                    <i class="fas fa-trophy"></i>
                    <h3>95%</h3>
                    <p>Success Rate</p>
                </div>
                <div class="social-stat">
                    <i class="fas fa-clock"></i>
                    <h3>24/7</h3>
                    <p>Support Available</p>
                </div>
            </div>
            <div class="social-links-section">
                <p>Follow my journey and see daily transformations:</p>
                <div class="social-links">
                    <a href="#" class="social-link instagram">
                        <i class="fab fa-instagram"></i>
                        <span>@coach_natru</span>
                    </a>
                    <a href="#" class="social-link youtube">
                        <i class="fab fa-youtube"></i>
                        <span>Coach Natru</span>
                    </a>
                    <a href="#" class="social-link facebook">
                        <i class="fab fa-facebook"></i>
                        <span>Coach Natru</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Coach Natru</h3>
                    <p>Transform your body and life with expert fitness coaching and personalized nutrition guidance.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                        <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About Coach</a></li>
                        <li><a href="results.html">Success Stories</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +91 98765 43210</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> Mumbai, India</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-links">
                    <a href="#privacy">Privacy Policy</a>
                    <a href="#terms">Terms of Service</a>
                    <a href="#refund">Refund Policy</a>
                </div>
                <p>&copy; 2025 Coach Natru. All rights reserved. | Designed for Netrapal Singh Yadav</p>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop" aria-label="Scroll to top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="script.js"></script>
    
    <!-- Contact Form JavaScript -->
    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Simple validation
            if (!data.name || !data.email || !data.phone || !data.goals || !data.message) {
                alert('Please fill in all required fields.');
                return;
            }
            
            // Simulate form submission
            const submitBtn = this.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            submitBtn.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                alert('Thank you for your message! I will get back to you within 24 hours.');
                this.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    </script>
</body>
</html>
